@keyframes qr--success {

    0%,
    100% {
        color: var(--SmartThemeBodyColor);
    }

    25%,
    75% {
        color: rgb(81, 163, 81);
    }
}

&.qr--success {
    animation-name: qr--success;
    animation-duration: 3s;
    animation-timing-function: linear;
    animation-delay: 0s;
    animation-iteration-count: 1;
}

#qr--bar {
    outline: none;
    margin: 0;
    transition: var(--animation-duration-2x);
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    order: 1;
    position: relative;

    >#qr--popoutTrigger {
        position: absolute;
        right: 0.25em;
        top: 0;
    }
}

/*hide QR popout for mobile*/
@media screen and (max-width: 1000px) {
    #qr--bar>#qr--popoutTrigger {
        display: none;
    }
}

#qr--bar.popoutVisible {
    padding-right: 2.5em;
}

#qr--popout {
    display: flex;
    flex-direction: column;
    padding: 0;
    z-index: 31;

    >.qr--header {
        flex: 0 0 auto;
        height: 2em;
        position: relative;

        >.qr--controls {
            >.qr--close {
                height: 15px;
                aspect-ratio: 1 / 1;
                font-size: 20px;
                opacity: 0.5;
                transition: all var(--animation-duration-2x);
            }
        }
    }

    >.qr--body {
        overflow-y: auto;
    }
}

#qr--bar,
#qr--popout>.qr--body {
    >.qr--buttons {
        --qr--color: transparent;

        &.qr--color {
            background-color: var(--qr--color);
        }

        &.qr--borderColor {
            background-color: transparent;
            border-left: 5px solid var(--qr--color);
            border-right: 5px solid var(--qr--color);
        }

        &:has(.qr--buttons.qr--color) {
            margin: 5px;
        }

        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 5px;
        width: 100%;

        >.qr--buttons {
            display: contents;

            &.qr--color {
                .qr--button:before {
                    content: '';
                    background-color: var(--qr--color);
                    position: absolute;
                    inset: -5px;
                    z-index: -1;
                }

                &.qr--borderColor {
                    .qr--button:before {
                        display: none;
                    }

                    &:before,
                    &:after {
                        content: '';
                        width: 5px;
                        background-color: var(--qr--color);
                    }
                }
            }
        }

        .qr--button {
            color: var(--SmartThemeBodyColor);
            // background-color: var(--black50a);
            border: 1px solid var(--SmartThemeBorderColor);
            border-radius: 10px;
            padding: 3px 5px;
            margin: 3px 0;
            cursor: pointer;
            transition: var(--animation-duration-2x);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;

            &:hover {
                background-color: rgb(30% 30% 30%);
            }

            .qr--hidden {
                display: none;
            }

            .qr--button-icon {
                margin: 0 0.5em;
            }

            >.qr--button-expander {
                display: none;
            }

            &.qr--hasCtx {
                >.qr--button-expander {
                    display: block;
                }
            }
        }
    }
}

.qr--button-expander {
    border-left: 1px solid;
    margin-left: 1em;
    text-align: center;
    width: 2em;

    &:hover {
        font-weight: bold;
    }
}

.ctx-blocker {
    /* backdrop-filter: blur(1px); */
    /* background-color: rgba(0 0 0 / 10%); */
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 999;
}

.ctx-menu {
    position: absolute;
    overflow: visible;
}

.ctx-menu .ctx-item .qr--hidden {
    display: none;
}

.list-group .list-group-item.ctx-header {
    font-weight: bold;
    cursor: default;
}

.ctx-item+.ctx-header {
    border-top: 1px solid;
}

.ctx-item {
    position: relative;
}

.ctx-expander {
    border-left: 1px solid;
    margin-left: 1em;
    text-align: center;
    width: 2em;
}

.ctx-expander:hover {
    font-weight: bold;
}

.ctx-sub-menu {
    position: absolute;
    top: 0;
    left: 100%;
}

@media screen and (max-width: 1000px) {
    .ctx-blocker {
        position: absolute;
    }

    .list-group .list-group-item.ctx-item {
        padding: 1em;
    }
}



#qr--settings {
    .qr--head {
        display: flex;
        align-items: baseline;
        gap: 1em;

        >.qr--title {
            font-weight: bold;
        }

        >.qr--actions {
            display: flex;
            flex-direction: row;
            align-items: baseline;
            gap: 0.5em;
        }
    }

    .qr--setList {
        >.qr--item {
            display: flex;
            flex-direction: row;
            gap: 0.5em;
            align-items: baseline;
            padding: 0 0.5em;

            >.drag-handle {
                padding: 0.75em;
            }

            >.qr--visible {
                flex: 0 0 auto;
                display: flex;
                flex-direction: row;
            }
        }
    }

    #qr--set-settings {
        #qr--injectInputContainer {
            flex-wrap: nowrap;
        }
    }

    #qr--set-qrList {
        .qr--set-qrListContents> {
            padding: 0 0.5em;

            >.qr--set-item .qr--set-itemAdder {
                display: flex;
                align-items: center;
                opacity: 0;
                transition: var(--animation-duration);
                margin: -2px 0 -11px 0;
                position: relative;

                .qr--actions {
                    display: flex;
                    gap: 0.25em;
                    flex: 0 0 auto;

                    .qr--action {
                        margin: 0;
                    }
                }

                &:before,
                &:after {
                    content: "";
                    display: block;
                    flex: 1 1 auto;
                    border: 1px solid;
                    margin: 0 1em;
                    height: 0;
                }

                &:hover,
                &:focus-within {
                    opacity: 1;
                }
            }

            >.qr--set-item .qr--content {
                display: flex;
                flex-direction: row;
                gap: 0.5em;
                align-items: baseline;
                padding: 0.25em 0;

                > :nth-child(2) {
                    flex: 0 0 auto;
                }

                > :nth-child(2) {
                    flex: 1 1 25%;
                }

                > :nth-child(3) {
                    flex: 0 0 auto;
                }

                > :nth-child(4) {
                    flex: 1 1 75%;
                }

                > :nth-child(5) {
                    flex: 0 1 auto;
                    display: flex;
                    gap: 0.25em;
                    justify-content: flex-end;
                    flex-wrap: wrap;
                }

                >.drag-handle {
                    padding: 0.75em;
                }

                .qr--set-itemLabelContainer {
                    display: flex;
                    align-items: center;
                    gap: 0.5em;

                    .qr--set-itemIcon:not(.fa-solid) {
                        display: none;
                    }

                    .qr--set-itemLabel {
                        min-width: 24px;
                    }
                }

                .qr--set-itemLabel,
                .qr--action {
                    margin: 0;
                }

                .qr--set-itemMessage {
                    font-size: smaller;
                }
            }


        }
    }

    .qr--set-qrListActions {
        display: flex;
        flex-direction: row;
        gap: 0.5em;
        justify-content: center;
        padding-bottom: 0.5em;
    }
}





#qr--qrOptions {
    display: flex;
    flex-direction: column;
    padding-right: 1px;

    >#qr--ctxEditor {
        .qr--ctxItem {
            display: flex;
            flex-direction: row;
            gap: 0.5em;
            align-items: baseline;
        }
    }

    >#qr--autoExec {
        .checkbox_label {
            text-wrap: nowrap;

            .fa-fw {
                margin-right: 2px;
            }
        }
    }
}



@media screen and (max-width: 750px) {
    body .popup:has(#qr--modalEditor) .popup-content>#qr--modalEditor {
        flex-direction: column;
        overflow: auto;

        >#qr--main {
            flex: 0 0 auto;
        }

        >#qr--main>.qr--labels {
            flex-direction: column;
        }

        >#qr--main>.qr--modal-messageContainer>#qr--modal-messageHolder {
            min-height: 50dvh;
            height: 50dvh;
        }
    }
}

.popup:has(#qr--modalEditor) {
    aspect-ratio: unset;
    width: unset;

    &:has(.qr--isExecuting.qr--minimized) {
        min-width: unset;
        min-height: unset;
        height: auto !important;
        width: min-content !important;
        position: absolute;
        right: 1em;
        top: 1em;
        left: unset;
        bottom: unset;
        margin: unset;
        padding: 0;

        &::backdrop {
            backdrop-filter: unset;
            background-color: transparent;
        }

        .popup-body {
            flex: 0 0 auto;
            height: min-content;
            width: min-content;
        }

        .popup-content {
            flex: 0 0 auto;
            margin-top: 0;

            >#qr--modalEditor {
                max-height: 50vh;

                >#qr--main,
                >#qr--resizeHandle,
                >#qr--qrOptions>h3,
                >#qr--qrOptions>#qr--modal-executeButtons,
                >#qr--qrOptions>#qr--modal-executeProgress {
                    display: none;
                }

                #qr--qrOptions {
                    width: auto;
                }

                #qr--modal-debugButtons .qr--modal-debugButton#qr--modal-maximize {
                    display: flex;
                }

                #qr--modal-debugButtons .qr--modal-debugButton#qr--modal-minimize {
                    display: none;
                }

                #qr--modal-debugState {
                    padding-top: 0;
                }
            }
        }
    }

    &:has(.qr--isExecuting) {
        .popup-controls {
            display: none;
        }

        .qr--highlight {
            position: absolute;
            z-index: 50000;
            pointer-events: none;
            background-color: rgb(47 150 180 / 0.5);

            &.qr--unresolved {
                background-color: rgb(255 255 0 / 0.5);
            }
        }

        .qr--highlight-secondary {
            position: absolute;
            z-index: 50000;
            pointer-events: none;
            border: 3px solid red;
        }
    }

    .popup-content {
        display: flex;
        flex-direction: column;

        >#qr--modalEditor {
            flex: 1 1 auto;
            display: flex;
            flex-direction: row;
            gap: 1em;
            overflow: hidden;

            &.qr--isExecuting {

                #qr--main>h3:first-child,
                #qr--main>.qr--labels,
                #qr--main>.qr--modal-messageContainer>.qr--modal-editorSettings,
                #qr--qrOptions>h3:first-child,
                #qr--qrOptions>#qr--ctxEditor,
                #qr--qrOptions>.qr--ctxEditorActions,
                #qr--qrOptions>.qr--ctxEditorActions+h3,
                #qr--qrOptions>.qr--ctxEditorActions+h3+div {
                    display: none;
                }

                #qr--main>.qr--modal-messageContainer>#qr--modal-messageHolder>#qr--modal-message {
                    visibility: hidden;
                }

                #qr--modal-debugButtons {
                    display: flex;

                    .menu_button:not(#qr--modal-minimize, #qr--modal-maximize) {
                        cursor: not-allowed;
                        opacity: 0.5;
                        pointer-events: none;
                        transition: var(--animation-duration-2x);
                        border-color: transparent;
                    }
                }

                &.qr--isPaused #qr--modal-debugButtons {
                    .menu_button:not(#qr--modal-minimize, #qr--modal-maximize) {
                        cursor: pointer;
                        opacity: 1;
                        pointer-events: all;

                        &#qr--modal-resume {
                            animation-name: qr--debugPulse;
                            animation-duration: 1500ms;
                            animation-timing-function: ease-in-out;
                            animation-delay: 0s;
                            animation-iteration-count: infinite;
                        }

                        &#qr--modal-resume {
                            border-color: rgb(81, 163, 81);
                        }

                        &#qr--modal-step {
                            border-color: var(--SmartThemeQuoteColor);
                        }

                        &#qr--modal-stepInto {
                            border-color: var(--SmartThemeQuoteColor);
                        }

                        &#qr--modal-stepOut {
                            border-color: var(--SmartThemeQuoteColor);
                        }
                    }
                }

                #qr--resizeHandle {
                    width: 6px;
                    background-color: var(--SmartThemeBorderColor);
                    border: 2px solid var(--SmartThemeBlurTintColor);
                    transition: border-color var(--animation-duration-2x), background-color var(--animation-duration-2x);
                    cursor: w-resize;

                    &:hover {
                        background-color: var(--SmartThemeQuoteColor);
                        border-color: var(--SmartThemeQuoteColor);
                    }
                }

                #qr--qrOptions {
                    width: var(--width, auto);
                }
            }

            >#qr--main {
                flex: 1 1 auto;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                >.qr--labels {
                    flex: 0 0 auto;
                    display: flex;
                    flex-direction: row;
                    gap: 0.5em;
                    padding: 1px;

                    >label,
                    >.label {
                        flex: 1 1 1px;
                        display: flex;
                        flex-direction: column;
                        position: relative;

                        &.qr--fit {
                            flex: 0 0 auto;
                            justify-content: center;
                        }

                        .qr--inputGroup {
                            display: flex;
                            align-items: baseline;
                            gap: 0.5em;

                            input {
                                flex: 1 1 auto;
                            }
                        }

                        .qr--labelText {
                            flex: 1 1 auto;
                        }

                        .qr--labelHint {
                            flex: 1 1 auto;
                        }

                        input {
                            flex: 0 0 auto;
                        }

                        .qr--modal-switcherList {
                            background-color: var(--stcdx--bgColor);
                            border: 1px solid var(--SmartThemeBorderColor);
                            backdrop-filter: blur(var(--SmartThemeBlurStrength));
                            border-radius: 10px;
                            font-size: smaller;
                            position: absolute;
                            top: 100%;
                            left: 0;
                            right: 0;
                            overflow: auto;
                            margin: 0;
                            padding: 0.5em;
                            max-height: 50vh;
                            list-style: none;
                            z-index: 40000;
                            max-width: 100%;

                            .qr--modal-switcherItem {
                                display: flex;
                                gap: 1em;
                                text-align: left;
                                opacity: 0.75;
                                transition: var(--animation-duration-2x);
                                cursor: pointer;

                                &:hover {
                                    opacity: 1;
                                }

                                &.qr--current {
                                    opacity: 1;

                                    .qr--label,
                                    .qr--id {
                                        font-weight: bold;
                                    }
                                }
                            }

                            .qr--label {
                                white-space: nowrap;

                                .menu_button {
                                    display: inline-block;
                                    height: min-content;
                                    width: min-content;
                                    margin: 0 0.5em 0 0;
                                }
                            }

                            .qr--id {
                                opacity: 0.5;

                                &:before {
                                    content: "[";
                                }

                                &:after {
                                    content: "]";
                                }
                            }

                            .qr--message {
                                height: 1lh;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                opacity: 0.5;
                            }
                        }
                    }
                }

                >.qr--modal-messageContainer {
                    flex: 1 1 auto;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;

                    >.qr--modal-editorSettings {
                        display: flex;
                        flex-wrap: wrap;
                        flex-direction: row;
                        column-gap: 1em;
                        color: var(--grey70);
                        font-size: smaller;
                        align-items: baseline;

                        >.checkbox_label {
                            white-space: nowrap;

                            >input {
                                font-size: inherit;
                            }
                        }
                    }

                    >#qr--modal-messageHolder {
                        flex: 1 1 auto;
                        display: grid;
                        text-align: left;
                        overflow: hidden;

                        &.qr--noSyntax {
                            >#qr--modal-messageSyntax {
                                display: none;
                            }

                            >#qr--modal-message {
                                background-color: var(--ac-style-color-background);
                                color: var(--ac-style-color-text);

                                &::-webkit-scrollbar,
                                &::-webkit-scrollbar-thumb {
                                    visibility: visible;
                                    cursor: unset;
                                }

                                &::selection {
                                    color: unset;
                                    background-color: rgba(*********** / 0.25);

                                    @supports (color: rgb(from white r g b / 0.25)) {
                                        background-color: rgb(from var(--ac-style-color-matchedText) r g b / 0.25);
                                    }
                                }
                            }
                        }

                        >#qr--modal-messageSyntax {
                            grid-column: 1;
                            grid-row: 1;
                            padding: 0;
                            margin: 0;
                            border: none;
                            overflow: hidden;
                            min-width: 100%;
                            width: 0;

                            >#qr--modal-messageSyntaxInner {
                                height: 100%;
                            }
                        }

                        >#qr--modal-message {
                            background-color: transparent;
                            color: transparent;
                            grid-column: 1;
                            grid-row: 1;
                            caret-color: var(--ac-style-color-text);
                            overflow: auto;

                            &::-webkit-scrollbar,
                            &::-webkit-scrollbar-thumb {
                                visibility: hidden;
                                cursor: default;
                            }

                            &::selection {
                                color: transparent;
                                background-color: rgba(*********** / 0.25);

                                @supports (color: rgb(from white r g b / 0.25)) {
                                    background-color: rgb(from var(--ac-style-color-matchedText) r g b / 0.25);
                                }
                            }
                        }

                        #qr--modal-message,
                        #qr--modal-messageSyntaxInner {
                            font-family: var(--monoFontFamily);
                            padding: 0.75em;
                            margin: 0;
                            resize: none;
                            line-height: 1.2;
                            border: 1px solid var(--SmartThemeBorderColor);
                            border-radius: 5px;
                            position: relative;
                        }
                    }
                }
            }

            #qr--modal-icon {
                height: 100%;
                aspect-ratio: 1 / 1;
            }

            #qr--modal-executeButtons {
                display: flex;
                gap: 1em;

                .qr--modal-executeButton {
                    border-width: 2px;
                    border-style: solid;
                    display: flex;
                    flex-direction: row;
                    gap: 0.5em;
                    padding: 0.5em 0.75em;

                    .qr--modal-executeComboIcon {
                        display: flex;
                    }
                }

                #qr--modal-execute {
                    transition: var(--animation-duration-2x);
                    filter: grayscale(0);

                    &.qr--busy {
                        cursor: wait;
                        opacity: 0.5;
                        filter: grayscale(1);
                    }
                }

                #qr--modal-execute {
                    border-color: rgb(81, 163, 81);
                }

                #qr--modal-pause,
                #qr--modal-stop {
                    cursor: default;
                    opacity: 0.5;
                    filter: grayscale(1);
                    pointer-events: none;
                }

                .qr--busy {

                    ~#qr--modal-pause,
                    ~#qr--modal-stop {
                        cursor: pointer;
                        opacity: 1;
                        filter: grayscale(0);
                        pointer-events: all;
                    }
                }

                #qr--modal-pause {
                    border-color: rgb(146, 190, 252);
                }

                #qr--modal-stop {
                    border-color: rgb(215, 136, 114);
                }
            }

            #qr--modal-debugButtons {
                display: none;
                gap: 1em;

                .qr--modal-debugButton {
                    aspect-ratio: 1.25 / 1;
                    width: 2.25em;
                    position: relative;

                    &:not(.fa-solid) {
                        border-width: 1px;
                        border-style: solid;

                        &:after {
                            content: '';
                            position: absolute;
                            inset: 3px;
                            background-color: var(--SmartThemeBodyColor);
                            mask-size: contain;
                            mask-position: center;
                            mask-repeat: no-repeat;
                        }
                    }

                    &#qr--modal-resume:after {
                        mask-image: url('/img/step-resume.svg');
                    }

                    &#qr--modal-step:after {
                        mask-image: url('/img/step-over.svg');
                    }

                    &#qr--modal-stepInto:after {
                        mask-image: url('/img/step-into.svg');
                    }

                    &#qr--modal-stepOut:after {
                        mask-image: url('/img/step-out.svg');
                    }

                    &#qr--modal-maximize {
                        display: none;
                    }
                }
            }

            #qr--modal-send_textarea {
                flex: 0 0 auto;
            }

            #qr--modal-executeProgress {
                --prog: 0;
                --progColor: rgb(146, 190, 252);
                --progFlashColor: rgb(215, 136, 114);
                --progSuccessColor: rgb(81, 163, 81);
                --progErrorColor: rgb(189, 54, 47);
                --progAbortedColor: rgb(215, 136, 114);
                flex: 0 0 auto;
                height: 0.5em;
                background-color: var(--black50a);
                position: relative;

                &:after {
                    content: '';
                    background-color: var(--progColor);
                    position: absolute;
                    inset: 0;
                    right: calc(100% - var(--prog) * 1%);
                    transition: var(--animation-duration-2x);
                }

                &.qr--paused:after {
                    animation-name: qr--progressPulse;
                    animation-duration: 1500ms;
                    animation-timing-function: ease-in-out;
                    animation-delay: 0s;
                    animation-iteration-count: infinite;
                }

                &.qr--aborted:after {
                    background-color: var(--progAbortedColor);
                }

                &.qr--success:after {
                    background-color: var(--progSuccessColor);
                }

                &.qr--error:after {
                    background-color: var(--progErrorColor);
                }
            }

            #qr--modal-executeErrors {
                display: none;

                &.qr--hasErrors {
                    display: block;
                }

                text-align: left;
                font-size: smaller;
                background-color: rgb(189, 54, 47);
                color: white;
                padding: 0.5em;
                overflow: auto;
                min-width: 100%;
                width: 0;
            }

            #qr--modal-executeResult {
                display: none;

                &.qr--hasResult {
                    display: block;
                }

                &:before {
                    content: 'Result: ';
                }

                text-align: left;
                font-size: smaller;
                background-color: rgb(81, 163, 81);
                color: white;
                padding: 0.5em;
                overflow: auto;
                min-width: 100%;
                width: 0;
                white-space: pre-wrap;
            }

            #qr--modal-debugState {
                display: none;

                &.qr--active {
                    display: block;
                }

                text-align: left;
                font-size: smaller;
                font-family: var(--monoFontFamily);
                // background-color: rgb(146, 190, 252);
                color: white;
                padding: 0.5em 0;
                overflow: auto;
                min-width: 100%;
                width: 0;
                white-space: pre-wrap;

                .qr--scope {
                    display: grid;
                    grid-template-columns: 0fr 1fr 1fr;
                    column-gap: 0em;

                    .qr--title {
                        grid-column: 1 / 4;
                        font-weight: bold;
                        font-family: var(--mainFontFamily);
                        background-color: var(--black50a);
                        padding: 0.25em;
                        margin-top: 0.5em;
                    }

                    .qr--var,
                    .qr--macro,
                    .qr--pipe {
                        display: contents;

                        &:nth-child(2n + 1) {

                            .qr--key,
                            .qr--val {
                                background-color: rgb(from var(--SmartThemeEmColor) r g b / 0.25);
                            }

                            .qr--val {
                                &:nth-child(2n) {
                                    background-color: rgb(from var(--SmartThemeEmColor) r g b / 0.125);
                                }

                                &:hover {
                                    background-color: rgb(from var(--SmartThemeEmColor) r g b / 0.5);
                                }
                            }
                        }

                        &:nth-child(2n) {
                            .qr--val {
                                &:nth-child(2n) {
                                    background-color: rgb(from var(--SmartThemeEmColor) r g b / 0.0625);
                                }

                                &:hover {
                                    background-color: rgb(from var(--SmartThemeEmColor) r g b / 0.5);
                                }
                            }
                        }

                        &.qr--isHidden {

                            .qr--key,
                            .qr--val {
                                opacity: 0.5;
                            }
                        }

                        .qr--val {
                            grid-column: 2 / 4;

                            &.qr--singleCol {
                                grid-column: unset;
                            }

                            &.qr--simple {

                                &:before,
                                &:after {
                                    content: '"';
                                    color: var(--SmartThemeQuoteColor);
                                }
                            }

                            &.qr--unresolved {
                                &:after {
                                    content: '-UNRESOLVED-';
                                    font-style: italic;
                                    color: var(--SmartThemeQuoteColor);
                                }
                            }
                        }
                    }

                    .qr--key {
                        margin-left: 0.5em;
                        padding-right: 1em;

                        &:after {
                            content: ": ";
                        }
                    }

                    .qr--pipe,
                    .qr--macro {
                        >.qr--key {
                            &:before {
                                content: "{{";
                            }

                            &:after {
                                content: "}}: ";
                            }
                        }
                    }

                    .qr--scope {
                        display: contents;

                        .qr--pipe {

                            .qr--key,
                            .qr--val {
                                opacity: 0.5;
                            }
                        }
                    }
                }

                .qr--stack {
                    display: grid;
                    grid-template-columns: 1fr 0fr;

                    .qr--title {
                        grid-column: 1 / 3;
                        font-weight: bold;
                        font-family: var(--mainFontFamily);
                        background-color: var(--black50a);
                        padding: 0.25em;
                        margin-top: 1em;
                    }

                    .qr--item {
                        display: contents;

                        &:nth-child(2n + 1) {

                            .qr--name,
                            .qr--source {
                                background-color: rgb(from var(--SmartThemeEmColor) r g b / 0.25);
                            }
                        }

                        .qr--name {
                            margin-left: 0.5em;
                        }

                        .qr--source {
                            opacity: 0.5;
                            text-align: right;
                            white-space: nowrap;
                        }
                    }
                }
            }
        }
    }
}

@keyframes qr--progressPulse {

    0%,
    100% {
        background-color: var(--progColor);
    }

    50% {
        background-color: var(--progFlashColor);
    }
}

@keyframes qr--debugPulse {

    0%,
    100% {
        border-color: rgb(81, 163, 81);
    }

    50% {
        border-color: rgb(146, 190, 252);
    }
}

.popup.qr--hide {
    opacity: 0 !important;

    &::backdrop {
        opacity: 0 !important;
    }
}

.popup.qr--hide::backdrop {
    opacity: 0 !important;
}



.popup:has(.qr--transferModal) {
    .popup-button-ok {
        &:after {
            content: 'Transfer';
            height: 0;
            overflow: hidden;
            font-weight: bold;
        }

        display: flex;
        align-items: center;
        flex-direction: column;
        white-space: pre;
        font-weight: normal;
        box-shadow: 0 0 0;
        transition: var(--animation-duration-2x);
    }

    .qr--copy {
        &:after {
            content: 'Copy';
            height: 0;
            overflow: hidden;
            font-weight: bold;
        }

        display: flex;
        align-items: center;
        flex-direction: column;
        white-space: pre;
        font-weight: normal;
        box-shadow: 0 0 0;
        transition: var(--animation-duration-2x);
    }

    &:has(.qr--transferSelect:focus) {
        .popup-button-ok {
            font-weight: bold;
            box-shadow: 0 0 10px;
        }

        &.qr--isCopy {
            .popup-button-ok {
                font-weight: normal;
                box-shadow: 0 0 0;
            }

            .qr--copy {
                font-weight: bold;
                box-shadow: 0 0 10px;
            }
        }
    }
}
