<div class="secretKeyManagerItem {{#if active}}active{{/if}}">
    <div class="secretKeyManagerItemInfo">
        <div class="secretKeyManagerItemHeader">
            <strong>{{label}}</strong>
            <small>{{value}}</small>
        </div>
        <div class="secretKeyManagerItemSubtitle">
            <strong>ID:</strong>
            <span class="secretKeyManagerItemId" data-action="copy-id" title="Copy ID" data-i18n="[title]Copy ID">{{id}}</span>
        </div>
    </div>
    <div class="secretKeyManagerItemActions">
        <div class="secretKeyManagerItemActionsRow">
            <button class="menu_button menu_button_icon {{#if active}}disabled{{/if}}" data-action="rotate-secret" data-id="{{id}}" title="Select" data-i18n="[title]Select">
                <i class="fa-fw fa-solid fa-check"></i>
            </button>
            <button class="menu_button menu_button_icon" data-action="copy-secret" data-id="{{id}}" title="Copy" data-i18n="[title]Copy">
                <i class="fa-fw fa-solid fa-copy"></i>
            </button>
        </div>
        <div class="secretKeyManagerItemActionsRow">
            <button class="menu_button menu_button_icon" data-action="rename-secret" data-id="{{id}}" title="Rename" data-i18n="[title]Rename">
                <i class="fa-fw fa-solid fa-pen-to-square"></i>
            </button>
            <button class="menu_button menu_button_icon" data-action="delete-secret" data-id="{{id}}" title="Delete" data-i18n="[title]Delete">
                <i class="fa-fw fa-solid fa-trash"></i>
            </button>
        </div>
    </div>
</div>
