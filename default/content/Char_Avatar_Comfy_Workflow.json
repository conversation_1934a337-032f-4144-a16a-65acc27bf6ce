{"3": {"inputs": {"seed": "%seed%", "steps": "%steps%", "cfg": "%scale%", "sampler_name": "%sampler%", "scheduler": "%scheduler%", "denoise": "%denoise%", "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["12", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "%model%"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "6": {"inputs": {"text": "%prompt%", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "%negative_prompt%", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "10": {"inputs": {"image": "%char_avatar%"}, "class_type": "ETN_LoadImageBase64", "_meta": {"title": "Load Image (Base64) [https://github.com/Acly/comfyui-tooling-nodes]"}}, "12": {"inputs": {"pixels": ["13", 0], "vae": ["4", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "13": {"inputs": {"upscale_method": "bicubic", "width": "%width%", "height": "%height%", "crop": "center", "image": ["10", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}}