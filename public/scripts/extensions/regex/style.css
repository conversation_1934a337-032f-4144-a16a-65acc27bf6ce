.regex_settings .menu_button {
    width: fit-content;
    display: flex;
    gap: 10px;
    flex-direction: row;
}

.regex_settings .checkbox {
    align-items: center;
}

.regex-script-container {
    margin-top: 10px;
    margin-bottom: 10px;
}

.regex-script-container:empty::after {
    content: attr(no-scripts-text);
    font-size: 0.95em;
    opacity: 0.7;
    display: block;
    text-align: center;
}

#scoped_scripts_block {
    opacity: 1;
    transition: opacity var(--animation-duration-2x) ease-in-out;
}

#scoped_scripts_block .move_to_scoped {
    display: none;
}

#global_scripts_block .move_to_global {
    display: none;
}

#scoped_scripts_block:not(:has(#regex_scoped_toggle:checked)) {
    opacity: 0.5;
}

.enable_scoped:checked ~ .regex-toggle-on {
    display: block;
}

.enable_scoped:checked ~ .regex-toggle-off {
    display: none;
}

.enable_scoped:not(:checked) ~ .regex-toggle-on {
    display: none;
}

.enable_scoped:not(:checked) ~ .regex-toggle-off {
    display: block;
}

.regex-script-label {
    align-items: baseline;
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 10px;
    padding: 0 5px;
    margin-top: 1px;
    margin-bottom: 1px;
}

.regex-script-label:has(.disable_regex:checked) .regex_script_name {
    text-decoration: line-through;
    filter: grayscale(0.5);
}

input.disable_regex,
input.enable_scoped {
    display: none !important;
}

.regex-toggle-off {
    cursor: pointer;
    opacity: 0.5;
    filter: grayscale(0.5);
    transition: opacity var(--animation-duration-2x) ease-in-out;
}

.regex-toggle-off:hover {
    opacity: 1;
    filter: none;
}

.regex-toggle-on {
    cursor: pointer;
}

.disable_regex:checked ~ .regex-toggle-off {
    display: block;
}

.disable_regex:checked ~ .regex-toggle-on {
    display: none;
}

.disable_regex:not(:checked) ~ .regex-toggle-off {
    display: none;
}

.disable_regex:not(:checked) ~ .regex-toggle-on {
    display: block;
}

#regex_info_block_wrapper {
    position: relative;
}

#regex_info_block {
    margin: 10px 0;
    padding: 5px 20px;
    font-size: 0.9em;
}

#regex_info_block_wrapper:has(#regex_info_block:empty) {
    display: none;
}

#regex_info_block_flags_hint {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
}

.regex_settings label[for="regex_bulk_edit"]:has(#regex_bulk_edit:checked) {
    color: var(--golden);
}

.regex_settings .regex-script-container .regex-script-label .regex_bulk_checkbox {
    margin-left: 5px;
    margin-right: 5px;
}

.regex_settings .regex_bulk_operations,
.regex_settings .regex_bulk_checkbox {
    display: none;
}

.regex_settings:has(#regex_bulk_edit:checked) .regex_bulk_operations {
    display: flex;
}

.regex_settings:has(#regex_bulk_edit:checked) .regex_bulk_checkbox {
    display: inline-grid;
}
